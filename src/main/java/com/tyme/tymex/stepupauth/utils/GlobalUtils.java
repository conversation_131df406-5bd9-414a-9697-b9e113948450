package com.tyme.tymex.stepupauth.utils;

import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto.AuthFactorRule;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.DynamoKeyType;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import lombok.experimental.UtilityClass;
import lombok.extern.log4j.Log4j2;

@Log4j2
@UtilityClass
public class GlobalUtils {

  public AuthFactorRule findFirstRuleByFactor(InitStepUpSessionDto obj, AuthFactor factor) {
    return obj.getAuthFactorRules().stream().filter(GlobalUtils.filterAuthRuleByFactor(factor)).findFirst()
        .orElse(obj.getAuthFactorRules().stream().map(AuthFactorRule::getFallbackRules).filter(Objects::nonNull).flatMap(List::stream).filter(GlobalUtils.filterAuthRuleByFactor(factor)).findFirst()
            .orElse(null));
  }

  public void buildConstraintViolationWithTemplate(ConstraintValidatorContext context, String fieldName, String message) {
    context.disableDefaultConstraintViolation();
    context.buildConstraintViolationWithTemplate(message).addPropertyNode(fieldName).addConstraintViolation();
  }

  public String toInboxPk(String raw){
    return DynamoKeyType.INBOX.getPrefix() + raw;
  }

  public String toStepUpSessionPk(String raw) {
    return DynamoKeyType.SESSION.getPrefix() + raw;
  }

  public String toDeviceBioEnrollmentPk(String profileId){
    return DynamoKeyType.PROFILE_ID.getPrefix() + profileId;
  }

  public String toStepUpSessionSk(String raw) {
    return DynamoKeyType.SESSION.getPrefix() + raw;
  }

  public String toFactorSk(AuthFactor raw) {
    return DynamoKeyType.FACTOR.getPrefix() + raw.getValue();
  }

  public String toLinkDeviceInfoPk(String profileId) {
    return DynamoKeyType.PROFILE_ID.getPrefix() + profileId;
  }

  public String toLinkDeviceInfoSk(String appId) {
    return DynamoKeyType.LINK_DEVICE_INFO.getPrefix() + appId;
  }

  public Predicate<StepUpEntity> filterByAuthId(String authId) {
    return entity -> entity.getSk().equals(toStepUpSessionSk(authId));
  }

  public Predicate<StepUpEntity> filterByFactor(AuthFactor factor) {
    return entity -> entity.getSk().equals(toFactorSk(factor));
  }

  public Predicate<AuthFactorRule> filterAuthRuleByFactor(AuthFactor factor) {
    return rule -> Objects.requireNonNull(factor).equals(rule.getFactor());
  }

  public String toVerifyAttemptSk(int attempt) {
    return DynamoKeyType.VERIFY.getPrefix().concat(String.valueOf(attempt));
  }

  public String getDeviceBioEnrollmentSk(){
    return DynamoKeyType.ENROLMENT_DATA.getPrefix() + AuthFactor.DEVICE_BIO;
  }
}