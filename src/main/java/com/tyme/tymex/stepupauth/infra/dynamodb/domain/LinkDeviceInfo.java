package com.tyme.tymex.stepupauth.infra.dynamodb.domain;

import com.tyme.tymex.stepupauth.infra.connector.model.DeviceIdType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

@Setter
@Getter
@SuperBuilder(toBuilder = true)
@ToString
@AllArgsConstructor
@NoArgsConstructor
@DynamoDbBean
public class LinkDeviceInfo {

  @Getter(onMethod = @__({
      @DynamoDbAttribute("pk"),
      @DynamoDbPartitionKey
  }))
  private String partitionKey;
  @Getter(onMethod = @__({
      @DynamoDbAttribute("sk"),
      @DynamoDbSortKey
  }))
  private String sk;
  @Getter(onMethod = @__(@DynamoDbAttribute("device_id")))
  private String deviceId;
  @Getter(onMethod = @__(@DynamoDbAttribute("device_id_type")))
  private DeviceIdType deviceIdType;
  @Getter(onMethod = @__(@DynamoDbAttribute("device_os")))
  private String deviceOs;
  @Getter(onMethod = @__(@DynamoDbAttribute("linked_at")))
  private Long linkedAt;

}
