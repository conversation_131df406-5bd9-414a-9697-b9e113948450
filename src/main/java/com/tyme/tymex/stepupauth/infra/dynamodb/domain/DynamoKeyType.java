package com.tyme.tymex.stepupauth.infra.dynamodb.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DynamoKeyType {
  SESSION("SESSION", "SESSION#"),
  FACTOR("FACTOR", "FACTOR#"),
  VERIFY("VERIFY", "VERIFY#"),
  PROFILE_ID("PROFILE_ID", "PROFILE_ID#"),
  ENROLMENT_DATA("ENROLMENT_DATA", "ENROLMENT_DATA#"),
  INBOX("INBOX", "INBOX#"),
  LINK_DEVICE_INFO("LINK_DEVICE_INFO", "TYPE#DEVICE#APP_ID#");


  private final String value;
  private final String prefix;
}