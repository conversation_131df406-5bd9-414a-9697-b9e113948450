package com.tyme.tymex.stepupauth.config;

import com.tyme.tymex.stepupauth.infra.dynamodb.domain.Inbox;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.LinkDeviceInfo;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.extensions.AutoGeneratedTimestampRecordExtension;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;


@Log4j2
@Configuration
public class DynamoDbConfig extends BaseConfig {

  public static final String STEP_UP_AUTH_TABLE = "step-up-auth";

  public static final String INBOX_TABLE = "step-up-auth-inbox";

  public static final String STEP_UP_AGGREGATION_TABLE = "step-up-aggregation";

  public DynamoDbConfig(Environment environment) {
    super(environment);
  }

  @Bean
  public DynamoDbEnhancedClient dynamoDbEnhancedClient(DynamoDbClient dynamoDbClient) {
    return DynamoDbEnhancedClient.builder()
        .dynamoDbClient(dynamoDbClient)
        .extensions(AutoGeneratedTimestampRecordExtension.create())
        .build();
  }

  @Bean
  public DynamoDbTable<Inbox> inboxDynamoDbTable(DynamoDbEnhancedClient enhancedClient) {
    return enhancedClient.table(INBOX_TABLE, TableSchema.fromBean(Inbox.class));
  }

  @Bean
  public DynamoDbTable<StepUpAuthDeviceBioEnrollment> deviceBioStepUpAuthTable(
      DynamoDbEnhancedClient enhancedClient) {
    return enhancedClient.table(STEP_UP_AUTH_TABLE,
        TableSchema.fromBean(StepUpAuthDeviceBioEnrollment.class));
  }

  @Bean
  public DynamoDbTable<StepUpEntity> stepUpAuthTable(DynamoDbEnhancedClient enhancedClient) {
    return enhancedClient.table(STEP_UP_AUTH_TABLE, TableSchema.fromBean(StepUpEntity.class));
  }

  @Bean
  public DynamoDbTable<LinkDeviceInfo> linkDeviceInfoTable(DynamoDbEnhancedClient enhancedClient) {
    return enhancedClient.table(STEP_UP_AGGREGATION_TABLE, TableSchema.fromBean(LinkDeviceInfo.class));
  }

}