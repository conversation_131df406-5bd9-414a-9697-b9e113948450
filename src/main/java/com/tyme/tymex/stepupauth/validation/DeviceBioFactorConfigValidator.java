
package com.tyme.tymex.stepupauth.validation;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.config.StepUpConfig;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.DeviceBioAuthConfig;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@RequiredArgsConstructor
public class DeviceBioFactorConfigValidator implements
    ConstraintValidator<ValidDeviceBioFactorConfig, InitStepUpSessionDto> {

  private final ObjectMapper mapper;
  private final StepUpConfig stepUpConfig;

  @Override
  public boolean isValid(InitStepUpSessionDto obj, ConstraintValidatorContext context) {
    if (null == GlobalUtils.findFirstRuleByFactor(obj, AuthFactor.DEVICE_BIO)) {
      return true;
    }

    if (obj.getIdentifierType() != IdentifierType.PROFILE_ID) {
      GlobalUtils.buildConstraintViolationWithTemplate(context, "identifierType",
          "must be PROFILE_ID");
      return false;
    }

    if (obj.getAuthConfig() == null) {
      GlobalUtils.buildConstraintViolationWithTemplate(context, "authConfig", "must not be null");
      return false;
    }
    var deviceBioConfigObj = obj.getAuthConfig().get(AuthFactor.DEVICE_BIO);
    if (deviceBioConfigObj == null) {
      GlobalUtils.buildConstraintViolationWithTemplate(context, "authConfig.DEVICE_BIO",
          "must not be null");
      return false;
    }

    var config = mapper.convertValue(deviceBioConfigObj, DeviceBioAuthConfig.class);

    if (StringUtils.isBlank(config.getDeviceId()) && StringUtils.isBlank(
        config.getInternalDeviceId())) {
      GlobalUtils.buildConstraintViolationWithTemplate(
          context,
          "authConfig.DEVICE_BIO.deviceId / authConfig.DEVICE_BIO.internalDeviceId",
          ".At least one of the deviceId or internalDeviceId must not be blank"
      );
      return false;
    }

    if (config.getLinkDeviceDays() != null
        && config.getLinkDeviceDays() < stepUpConfig.getLinkDeviceThresholdInDays()) {
      GlobalUtils.buildConstraintViolationWithTemplate(context,
          "authConfig.DEVICE_BIO.linkDeviceDays",
          "must be greater than " + stepUpConfig.getLinkDeviceThresholdInDays() + " days");
      return false;
    }
    if (config.getDeviceBioEnrollDays() != null
        && config.getDeviceBioEnrollDays() < stepUpConfig.getDeviceBioEnrollThresholdInDays()) {
      GlobalUtils.buildConstraintViolationWithTemplate(context,
          "authConfig.DEVICE_BIO.deviceBioEnrollDays",
          "must be greater than " + stepUpConfig.getDeviceBioEnrollThresholdInDays() + " days");
      return false;
    }

    return true;
  }
}

