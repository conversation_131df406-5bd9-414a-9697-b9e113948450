package com.tyme.tymex.stepupauth.service.impl;

import static com.tyme.tymex.stepupauth.domain.AuthFactor.DEVICE_BIO;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.config.StepUpConfig;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.DeviceBioAuthConfig;
import com.tyme.tymex.stepupauth.domain.DeviceBioEnrollStatus;
import com.tyme.tymex.stepupauth.infra.connector.model.DeviceIdType;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.LinkDeviceInfo;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpAuthDeviceBioEnrollment;
import com.tyme.tymex.stepupauth.repository.LinkDeviceInfoRepo;
import com.tyme.tymex.stepupauth.repository.StepUpAuthDeviceBioEnrollmentRepo;
import com.tyme.tymex.stepupauth.service.AuthFactorEligibilityValidator;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.EnumSet;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Log4j2
public class DeviceBioEligibilityValidator implements AuthFactorEligibilityValidator {

  @Value("${auth-token.request-token.link-device-eligibility-check:true}")
  private boolean enableLinkDeviceEligibilityCheck;

  private final LinkDeviceInfoRepo linkDeviceInfoRepo;
  private final ObjectMapper objectMapper;
  private final StepUpConfig stepUpConfig;
  private final StepUpAuthDeviceBioEnrollmentRepo stepUpAuthDeviceBioEnrollmentRepo;
  private final Set<DeviceBioEnrollStatus> eligibleStatus = EnumSet.of(
      DeviceBioEnrollStatus.ENROLLED, DeviceBioEnrollStatus.RE_ENROLLED);

  @Override
  public boolean checkGlobalEligibility(InitStepUpSessionDto stepUpSession) {
    DeviceBioAuthConfig deviceBioAuthConfig = objectMapper.convertValue(
        stepUpSession.getAuthConfig().get(DEVICE_BIO), DeviceBioAuthConfig.class);

    if (!enableLinkDeviceEligibilityCheck) {
      return true;
    }

    Optional<LinkDeviceInfo> linkDeviceInfoOptional = linkDeviceInfoRepo.findLinkDeviceInfoByProfileIdAndAppId(
        stepUpSession.getIdentifierId(), stepUpSession.getAppId());

    if (linkDeviceInfoOptional.isEmpty() || linkDeviceInfoOptional.get().getDeviceId() == null) {
      log.warn("flowId: {}. No linked device info found", stepUpSession.getFlowId());
      return false;
    }

    LinkDeviceInfo linkDeviceInfo = linkDeviceInfoOptional.get();
    DeviceIdType deviceIdType = linkDeviceInfo.getDeviceIdType();

    String expectedDeviceId = (deviceIdType == DeviceIdType.AUTH_DEVICE_ID)
        ? deviceBioAuthConfig.getDeviceId()
        : deviceBioAuthConfig.getInternalDeviceId();

    if (!linkDeviceInfo.getDeviceId().equals(expectedDeviceId)) {
      log.warn("flowId: {}. Invalid linked deviceId", stepUpSession.getFlowId());
      return false;
    }

    if (linkDeviceInfo.getLinkedAt() == null) {
      log.warn("flowId: {}. Invalid format linkedAt", stepUpSession.getFlowId());
      return false;
    }

    Long linkDeviceThresholdDays = Optional.ofNullable(deviceBioAuthConfig.getLinkDeviceDays())
        .orElseGet(stepUpConfig::getLinkDeviceThresholdInDays);

    Instant deviceLinkThresholdTime = Instant.now().minus(linkDeviceThresholdDays, ChronoUnit.DAYS);

    Instant linkedAtInstant = Instant.ofEpochMilli(linkDeviceInfo.getLinkedAt());
    if (linkedAtInstant.isAfter(deviceLinkThresholdTime)) {
      log.warn("flowId: {}. The device is linked for less time than expected",
          stepUpSession.getFlowId());
      return false;
    }

    Optional<StepUpAuthDeviceBioEnrollment> enrollDataOpt = stepUpAuthDeviceBioEnrollmentRepo.findDeviceBioEnrollmentByProfileId(
        stepUpSession.getIdentifierId());
    if (enrollDataOpt.isEmpty()) {
      log.warn("flowId: {}. There is no device bio enrollment data.", stepUpSession.getFlowId());
      return false;
    }

    StepUpAuthDeviceBioEnrollment enrollData = enrollDataOpt.get();
    if (!eligibleStatus.contains(DeviceBioEnrollStatus.valueOf(enrollData.getStatus()))) {
      log.warn("flowId: {}. This device has disabled device bio enrollment.",
          stepUpSession.getFlowId());
      return false;
    }

    Long deviceBioEnrollThresholdDays = Optional.ofNullable(
            deviceBioAuthConfig.getDeviceBioEnrollDays())
        .orElseGet(stepUpConfig::getDeviceBioEnrollThresholdInDays);

    Instant deviceBioEnrollThresholdTime = Instant.now()
        .minus(deviceBioEnrollThresholdDays, ChronoUnit.DAYS);

    Instant enrollTime = Instant.ofEpochMilli(enrollData.getEventTime());

    if (enrollTime.isAfter(deviceBioEnrollThresholdTime)) {
      log.warn("flowId: {}. The device bio is enrolled for less time than expected",
          stepUpSession.getFlowId());
      return false;
    }
    return true;
  }

  @Override
  public AuthFactor getAuthFactor() {
    return DEVICE_BIO;
  }

}
