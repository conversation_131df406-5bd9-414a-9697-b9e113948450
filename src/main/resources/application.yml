server:
  port: 8112

spring:
  application:
    name: step-up-auth-svc
  profiles:
    active: "${SPRING_PROFILES_ACTIVE:local}"
    group:
      local: local
      dev: dev
      test: test
      tst: tst
      stg: stg
      prd: prd
  jackson:
    default-property-inclusion: non_null
    deserialization:
      read-unknown-enum-values-as-null: true
      FAIL_ON_UNKNOWN_PROPERTIES: false
  cloud:
    aws:
      region: ${DEFAULT_REGION:ap-southeast-1}
      dynamodb:
        endpoint: https://dynamodb.${DEFAULT_REGION:ap-southeast-1}.amazonaws.com
      sqs:
        device-bio-enrollment-sqs: ${DEVICE_BIO_ENROLLMENT_SQS:tp-step-up-device-bio-enrollment-sqs}
    openfeign:
      client:
        config:
          default:
            logger-level: basic
            connect-timeout: 10000
            read-timeout: 30000
          client-profile-ingress:
            url: "https://${CLIENT_PROFILE_INGRESS_DOMAIN}"
            api-key: ${CLIENT_PROFILE_INGRESS_API_KEY:}


management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true
      show-details: "always"

step-up:
  min-expired-in-seconds: ${MIN_SESSION_EXPIRED_IN_SECONDS:15}
  default-expired-in-seconds: ${DEFAULT_SESSION_EXPIRED_IN_SECONDS:300}
  max-expired-in-seconds: ${MAX_SESSION_EXPIRED_IN_SECONDS:900}
  link-device-threshold-in-days: ${LINK_DEVICE_THRESHOLD_IN_DAYS:1}
  device-bio-enroll-threshold-in-days: ${DEVICE_BIO_ENROLL_THRESHOLD_IN_DAYS:1}


config:
  sqs:
    retry:
      backoff-exponent: 3
      backoff-multiplier: 0.5