package com.tyme.tymex.stepupauth.cucumber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto.AuthFactorRule;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.DeviceBioAuthConfig;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.ProfileInfo;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.connector.model.profile.ProfilePhoneData;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import jakarta.validation.ConstraintViolationException;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.service.StepUpService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import io.cucumber.java.Before;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Log4j2
@SpringBootTest
public class SessionInitAPICucumberSteps extends StepUpAuthApplicationTestsBase {

    @Autowired
    private StepUpService stepUpService;

    @Autowired
    private StepUpRepo stepUpRepo;

    @Autowired
    private ObjectMapper objectMapper;

    private String profileId;
    private String flowId;
    private String flowName;
    private String appId;
    private InitStepUpSessionDto initRequest;
    private StepUpSessionResponse sessionResponse;
    private Exception thrownException;
    private ProfileInfo profileInfo;

    @Before
    public void init() {
        profileId = "test-profile-" + UUID.randomUUID();
        flowId = "test-flow-" + UUID.randomUUID();
        flowName = "TEST_FLOW";
        appId = "test-app";
        initRequest = null;
        sessionResponse = null;
        thrownException = null;
        profileInfo = null;
    }

    @Given("A valid profile exists in the system")
    public void aValidProfileExistsInTheSystem() {
        // Profile will be mocked by the MockWebServer in InitialExtension
        // The mock server will return valid profile data for profileIds starting with "test-profile-"
        System.out.println(profileId);
        profileInfo = ProfileInfo.builder()
                .id(profileId)
                .personPhoneData(ProfilePhoneData.builder()
                        .dialCode("+84")
                        .phoneNumber("+84909418781")
                        .build())
                .build();
        log.info("Profile info: {}", profileInfo.toString());
        log.info("Profile service mock server will return valid profile info for profileId: {}", profileId);
    }

    @Given("The profile has valid {string} information")
    public void theProfileHasValidInformation(String dataType) {
        switch (dataType) {
            case "phone":
                // This is already covered in the previous step for phone
                Assertions.assertNotNull(profileInfo);
                Assertions.assertNotNull(profileInfo.personPhoneData());
                Assertions.assertNotNull(profileInfo.personPhoneData().dialCode());
                Assertions.assertNotNull(profileInfo.personPhoneData().phoneNumber());
                log.info("Profile has valid phone data: dialCode={}, phoneNumber={}",
                        profileInfo.personPhoneData().dialCode(),
                        profileInfo.personPhoneData().phoneNumber());
                break;
            case "device":
                // For device data, we just log since device info comes from client request
                log.info("Profile has valid device information for DEVICE_BIO factor");
                break;
            default:
                log.warn("Unknown data type: {}", dataType);
        }
    }

    @When("I initialize a step-up session with {string} factor and {string}")
    public void iInitializeAStepUpSessionWithFactorAndIdentifier(String factor, String identifierType) {
        // Parse factor and identifier type
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        IdentifierType idType = IdentifierType.valueOf(identifierType);

        // Setup authConfig based on factor type
        Map<AuthFactor, Object> authConfig = new HashMap<>();
        setupAuthConfigForFactor(authFactor, authConfig);

        initRequest = InitStepUpSessionDto.builder()
                .authFactorRules(List.of(AuthFactorRule.builder()
                        .factor(authFactor)
                        .build()))
                .authConfig(authConfig)
                .identifierId(profileId)
                .identifierType(idType)
                .flowId(flowId)
                .flowName(flowName)
                .appId(appId)
                .expiredIn(600)
                .build();
        log.info("Initializing step-up session with {} factor and {} for profileId: {}", factor, identifierType, profileId);

        try {
            sessionResponse = stepUpService.initStepUpSession(initRequest);
            log.info("Session initialized successfully with authId: {}", sessionResponse.getStepUpAuthId());
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception occurred during {} session initialization: {}", factor, e.getMessage());
        }
    }

    @When("I initialize a step-up session with {string} factor and {string} with authConfig {string}")
    public void iInitializeAStepUpSessionWithFactorAndIdentifierWithAuthConfig(String factor, String identifierType, String authConfigParams) {
        // Parse factor and identifier type
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        IdentifierType idType = IdentifierType.valueOf(identifierType);

        // Setup authConfig based on factor type and parameters
        Map<AuthFactor, Object> authConfig = new HashMap<>();
        setupAuthConfigForFactorWithParams(authFactor, authConfig, authConfigParams);

        initRequest = InitStepUpSessionDto.builder()
                .authFactorRules(List.of(AuthFactorRule.builder()
                        .factor(authFactor)
                        .build()))
                .authConfig(authConfig)
                .identifierId(profileId)
                .identifierType(idType)
                .flowId(flowId)
                .flowName(flowName)
                .appId(appId)
                .expiredIn(600)
                .build();
        log.info("Initializing step-up session with {} factor and {} for profileId: {} with authConfig: {}",
                factor, identifierType, profileId, authConfigParams);

        try {
            sessionResponse = stepUpService.initStepUpSession(initRequest);
            log.info("Session initialized successfully with authId: {}", sessionResponse.getStepUpAuthId());
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception occurred during {} session initialization: {}", factor, e.getMessage());
        }
    }

    /**
     * Helper method to setup authConfig based on factor type
     */
    private void setupAuthConfigForFactor(AuthFactor factor, Map<AuthFactor, Object> authConfig) {
        switch (factor) {
            case OTP:
                // OTP doesn't need authConfig - will be enriched from profile
                break;
            case DEVICE_BIO:
                DeviceBioAuthConfig deviceBioConfig = DeviceBioAuthConfig.builder()
                        .deviceId("ad4c48c67836ff97")
                        .internalDeviceId("ad4c48c67836ff97")
                        .linkDeviceDays(10L)
                        .deviceBioEnrollDays(10L)
                        .build();
                authConfig.put(AuthFactor.DEVICE_BIO, deviceBioConfig);
                break;
            default:
                log.warn("No authConfig setup defined for factor: {}", factor);
        }
    }

    /**
     * Helper method to verify factor config data based on factor type and data type
     */
    private void verifyFactorConfigData(AuthFactor factor, String dataType, Object factorConfig) {
        switch (factor) {
            case OTP:
                if ("phone".equals(dataType)) {
                    // Verify OTP config has phone data (cellphone, dialCode)
                    log.info("Verifying OTP config has phone data");
                }
                break;
            case DEVICE_BIO:
                if ("device".equals(dataType)) {
                    // Verify DEVICE_BIO config has device data
                    DeviceBioAuthConfig deviceBioConfig = objectMapper.convertValue(factorConfig, DeviceBioAuthConfig.class);
                    Assertions.assertEquals("ad4c48c67836ff97", deviceBioConfig.getDeviceId(), "Device ID should match");
                    Assertions.assertEquals("ad4c48c67836ff97", deviceBioConfig.getInternalDeviceId(), "Internal Device ID should match");
                    Assertions.assertEquals(10L, deviceBioConfig.getLinkDeviceDays(), "Link device days should match");
                    Assertions.assertEquals(10L, deviceBioConfig.getDeviceBioEnrollDays(), "Device bio enroll days should match");
                }
                break;
            default:
                log.warn("No verification defined for factor: {} with dataType: {}", factor, dataType);
        }
    }

    @Then("The session should be created successfully")
    public void theSessionShouldBeCreatedSuccessfully() {
        Assertions.assertNull(thrownException, "No exception should be thrown");
        Assertions.assertNotNull(sessionResponse, "Session response should not be null");
        Assertions.assertNotNull(sessionResponse.getStepUpAuthId(), "Step-up auth ID should not be null");
        log.info("Session created successfully with ID: {}", sessionResponse.getStepUpAuthId());
    }

    @Then("The session should have {string} factor configured")
    public void theSessionShouldHaveFactorConfigured(String factor) throws JsonProcessingException {
        AuthFactor authFactor = AuthFactor.valueOf(factor);

        Assertions.assertNotNull(sessionResponse.getAuthFactorRules(), "Auth factor rules should not be null");
        Assertions.assertFalse(sessionResponse.getAuthFactorRules().isEmpty(), "Auth factor rules should not be empty");

        // Parse JSON string to List<AuthFactorRule>
        List<AuthFactorRule> authFactorRules = objectMapper.readValue(
                sessionResponse.getAuthFactorRules(),
                new TypeReference<List<AuthFactorRule>>() {}
        );

        boolean hasRequestedFactor = authFactorRules.stream()
                .anyMatch(rule -> rule.getFactor() == authFactor);
        Assertions.assertTrue(hasRequestedFactor, "Session should have " + factor + " factor configured");
        log.info("Session has {} factor configured", factor);
    }

    @Then("The {string} configuration should be enriched with profile {string} data")
    public void theConfigurationShouldBeEnrichedWithProfileData(String factor, String dataType) {
        AuthFactor authFactor = AuthFactor.valueOf(factor);

        // Verify that the session entity in database has enriched config
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertNotNull(sessionEntity.getFactorConfigs(), "Factor configs should not be null");

        Object factorConfig = sessionEntity.getFactorConfigs().get(authFactor);
        Assertions.assertNotNull(factorConfig, factor + " config should not be null");

        // Verify specific data based on factor and data type
        verifyFactorConfigData(authFactor, dataType, factorConfig);

        log.info("{} configuration has been enriched with profile {} data", factor, dataType);
    }

    @Then("The session status should be IN_PROGRESS")
    public void theSessionStatusShouldBeINPROGRESS() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);
        
        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(StepUpStatus.IN_PROGRESS, sessionEntity.getOverallStatus(), 
                "Session status should be IN_PROGRESS");
        log.info("Session status is IN_PROGRESS");
    }

    @Then("The current factor should be {string}")
    public void theCurrentFactorShouldBe(String factor) {
        AuthFactor authFactor = AuthFactor.valueOf(factor);

        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(authFactor, sessionEntity.getCurrentFactor(),
                "Current factor should be " + factor);
        log.info("Current factor is {}", factor);
    }

    @Given("A profile does not exist in the system")
    public void aProfileDoesNotExistInTheSystem() {
        // Use a profileId that doesn't start with "test-profile-" so mock server returns 404
        profileId = "non-existent-profile-" + UUID.randomUUID();
        log.info("Using non-existent profileId: {} - mock server will return 404", profileId);
    }

    @Then("The service should return an error indicating profile not found")
    public void theServiceShouldReturnAnErrorIndicatingProfileNotFound() {
        Assertions.assertNotNull(thrownException, "Exception should be thrown");
        Assertions.assertTrue(thrownException instanceof DomainException, 
                "Exception should be DomainException");
        
        DomainException domainException = (DomainException) thrownException;
        Assertions.assertEquals(ErrorCode.PROFILE_NOT_FOUND, domainException.getErrorCode(), 
                "Error code should be PROFILE_NOT_FOUND");
        log.info("Service correctly returned PROFILE_NOT_FOUND error");
    }

    @Given("A profile exists but has no {string} information")
    public void aProfileExistsButHasNoInformation(String dataType) {
        // Use special profileId patterns that mock server can recognize
        switch (dataType) {
            case "phone":
                profileId = "test-profile-no-phone-" + UUID.randomUUID();
                log.info("Using profileId without phone data: {}", profileId);
                break;
            case "device":
                profileId = "test-profile-no-device-" + UUID.randomUUID();
                log.info("Using profileId without device data: {}", profileId);
                break;
            default:
                log.warn("Unknown data type: {}", dataType);
        }
    }

    @Then("The service should return an error indicating invalid profile data")
    public void theServiceShouldReturnAnErrorIndicatingInvalidProfileData() {
        Assertions.assertNotNull(thrownException, "Exception should be thrown");
        // The specific error handling for missing phone data may vary based on implementation
        log.info("Service correctly returned error for invalid profile data: {}", thrownException.getMessage());
    }

    // ========================================
    // DEVICE_BIO FACTOR STEP DEFINITIONS
    // ========================================

    @Given("The profile has valid device information")
    public void theProfileHasValidDeviceInformation() {
        // Extend profile info to include device data
        if (profileInfo == null) {
            profileInfo = ProfileInfo.builder()
                    .id(profileId)
                    .build();
        }
        // Add device information to profile
        log.info("Profile has valid device information for DEVICE_BIO factor");
    }

    @When("I initialize a step-up session with DEVICE_BIO factor and PROFILE_ID")
    public void iInitializeAStepUpSessionWithDEVICEBIOFactorAndPROFILEID() {
        // Setup device bio config with test data
        Map<AuthFactor, Object> authConfig = new HashMap<>();
        DeviceBioAuthConfig deviceBioConfig = DeviceBioAuthConfig.builder()
                .deviceId("ad4c48c67836ff97")
                .internalDeviceId("ad4c48c67836ff97")
                .linkDeviceDays(10L)
                .deviceBioEnrollDays(10L)
                .build();
        authConfig.put(AuthFactor.DEVICE_BIO, deviceBioConfig);

        initRequest = InitStepUpSessionDto.builder()
                .authFactorRules(List.of(AuthFactorRule.builder()
                        .factor(AuthFactor.DEVICE_BIO)
                        .build()))
                .authConfig(authConfig)
                .identifierId(profileId)
                .identifierType(IdentifierType.PROFILE_ID)
                .flowId(flowId)
                .flowName(flowName)
                .appId(appId)
                .expiredIn(600)
                .build();

        DeviceBioAuthConfig configForLogging = (DeviceBioAuthConfig) authConfig.get(AuthFactor.DEVICE_BIO);
        log.info("Initializing step-up session with DEVICE_BIO factor for profileId: {} with deviceId: {}",
                profileId, configForLogging != null ? configForLogging.getDeviceId() : "null");

        try {
            sessionResponse = stepUpService.initStepUpSession(initRequest);
            log.info("Session initialized successfully with authId: {}", sessionResponse.getStepUpAuthId());
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception occurred during DEVICE_BIO session initialization: {}", e.getMessage());
        }
    }

    @Then("The session should have DEVICE_BIO factor configured")
    public void theSessionShouldHaveDEVICEBIOFactorConfigured() throws JsonProcessingException {
        Assertions.assertNotNull(sessionResponse.getAuthFactorRules(), "Auth factor rules should not be null");
        Assertions.assertFalse(sessionResponse.getAuthFactorRules().isEmpty(), "Auth factor rules should not be empty");

        // Parse JSON string to List<AuthFactorRule>
        List<AuthFactorRule> authFactorRules = objectMapper.readValue(
                sessionResponse.getAuthFactorRules(),
                new TypeReference<List<AuthFactorRule>>() {}
        );

        boolean hasDeviceBioFactor = authFactorRules.stream()
                .anyMatch(rule -> rule.getFactor() == AuthFactor.DEVICE_BIO);
        Assertions.assertTrue(hasDeviceBioFactor, "Session should have DEVICE_BIO factor configured");
        log.info("Session has DEVICE_BIO factor configured");
    }

    @Then("The DEVICE_BIO configuration should contain the provided device data")
    public void theDEVICEBIOConfigurationShouldContainTheProvidedDeviceData() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertNotNull(sessionEntity.getFactorConfigs(), "Factor configs should not be null");

        Object deviceBioConfigObj = sessionEntity.getFactorConfigs().get(AuthFactor.DEVICE_BIO);
        Assertions.assertNotNull(deviceBioConfigObj, "DEVICE_BIO config should not be null");

        DeviceBioAuthConfig deviceBioConfig = objectMapper.convertValue(deviceBioConfigObj, DeviceBioAuthConfig.class);
        Assertions.assertEquals("ad4c48c67836ff97", deviceBioConfig.getDeviceId(), "Device ID should match");
        Assertions.assertEquals("ad4c48c67836ff97", deviceBioConfig.getInternalDeviceId(), "Internal Device ID should match");
        Assertions.assertEquals(10L, deviceBioConfig.getLinkDeviceDays(), "Link device days should match");
        Assertions.assertEquals(10L, deviceBioConfig.getDeviceBioEnrollDays(), "Device bio enroll days should match");

        log.info("DEVICE_BIO configuration contains the provided device data: deviceId={}, linkDeviceDays={}, deviceBioEnrollDays={}",
                deviceBioConfig.getDeviceId(), deviceBioConfig.getLinkDeviceDays(), deviceBioConfig.getDeviceBioEnrollDays());
    }

    @Then("The current factor should be DEVICE_BIO")
    public void theCurrentFactorShouldBeDEVICEBIO() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(AuthFactor.DEVICE_BIO, sessionEntity.getCurrentFactor(),
                "Current factor should be DEVICE_BIO");
        log.info("Current factor is DEVICE_BIO");
    }





    @Given("The profile has device information but device is not linked")
    public void theProfileHasDeviceInformationButDeviceIsNotLinked() {
        // Profile has device info but device is not yet linked for biometric authentication
        if (profileInfo == null) {
            profileInfo = ProfileInfo.builder()
                    .id(profileId)
                    .build();
        }
        // Use a different deviceId to simulate unlinked device
        profileId = "test-profile-unlinked-device-" + UUID.randomUUID();
        log.info("Profile has device information but device is not linked yet for profileId: {}", profileId);
    }

    @Then("The DEVICE_BIO configuration should indicate device linking is required")
    public void theDEVICEBIOConfigurationShouldIndicateDeviceLinkingIsRequired() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertNotNull(sessionEntity.getFactorConfigs(), "Factor configs should not be null");

        Object deviceBioConfig = sessionEntity.getFactorConfigs().get(AuthFactor.DEVICE_BIO);
        Assertions.assertNotNull(deviceBioConfig, "DEVICE_BIO config should not be null");

        // The config should indicate that device linking is required
        log.info("DEVICE_BIO configuration indicates device linking is required");
    }

    @Given("The profile has a linked device with biometric data")
    public void theProfileHasALinkedDeviceWithBiometricData() {
        // Profile has a device that is already linked and has biometric data
        if (profileInfo == null) {
            profileInfo = ProfileInfo.builder()
                    .id(profileId)
                    .build();
        }
        // Use a special profileId to simulate linked device with biometric data
        profileId = "test-profile-linked-device-" + UUID.randomUUID();
        log.info("Profile has a linked device with biometric data for profileId: {}", profileId);
    }

    @Then("The DEVICE_BIO configuration should include existing device information")
    public void theDEVICEBIOConfigurationShouldIncludeExistingDeviceInformation() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertNotNull(sessionEntity.getFactorConfigs(), "Factor configs should not be null");

        Object deviceBioConfig = sessionEntity.getFactorConfigs().get(AuthFactor.DEVICE_BIO);
        Assertions.assertNotNull(deviceBioConfig, "DEVICE_BIO config should not be null");

        // The config should include existing device information
        log.info("DEVICE_BIO configuration includes existing device information");
    }
}
