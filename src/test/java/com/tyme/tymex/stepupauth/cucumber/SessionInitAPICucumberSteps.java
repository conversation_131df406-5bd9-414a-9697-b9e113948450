package com.tyme.tymex.stepupauth.cucumber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto.AuthFactorRule;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.DeviceBioAuthConfig;
import com.tyme.tymex.stepupauth.domain.FacialAuthConfig;
import com.tyme.tymex.stepupauth.domain.OtpAuthConfig;
import com.tyme.tymex.stepupauth.domain.PasscodeAuthConfig;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.ProfileInfo;
import com.tyme.tymex.stepupauth.domain.ProfilePhoneData;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.exception.DomainException;
import com.tyme.tymex.stepupauth.exception.ErrorCode;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.repository.entity.StepUpEntity;
import com.tyme.tymex.stepupauth.service.StepUpService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import io.cucumber.java.Before;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Log4j2
@SpringBootTest
public class SessionInitAPICucumberSteps extends StepUpAuthApplicationTestsBase {

    @Autowired
    private StepUpService stepUpService;

    @Autowired
    private StepUpRepo stepUpRepo;

    @Autowired
    private ObjectMapper objectMapper;

    private String profileId;
    private String flowId;
    private String flowName;
    private String appId;
    private InitStepUpSessionDto initRequest;
    private StepUpSessionResponse sessionResponse;
    private Exception thrownException;
    private ProfileInfo profileInfo;

    @Before
    public void init() {
        profileId = "test-profile-" + UUID.randomUUID();
        flowId = "test-flow-" + UUID.randomUUID();
        flowName = "TEST_FLOW";
        appId = "test-app";
        initRequest = null;
        sessionResponse = null;
        thrownException = null;
        profileInfo = null;
    }

    // ========================================
    // GIVEN STEP DEFINITIONS
    // ========================================

    @Given("A valid profile exists in the system")
    public void aValidProfileExistsInTheSystem() {
        // Profile will be mocked by the MockWebServer in InitialExtension
        // The mock server will return valid profile data for profileIds starting with "test-profile-"
        profileInfo = ProfileInfo.builder()
                .id(profileId)
                .personPhoneData(ProfilePhoneData.builder()
                        .dialCode("+84")
                        .phoneNumber("+84909418781")
                        .build())
                .build();
        log.info("Profile service mock server will return valid profile info for profileId: {}", profileId);
    }

    @Given("The profile has valid {string} information")
    public void theProfileHasValidInformation(String dataType) {
        switch (dataType) {
            case "phone":
                // This is already covered in the previous step for phone
                Assertions.assertNotNull(profileInfo);
                Assertions.assertNotNull(profileInfo.personPhoneData());
                Assertions.assertNotNull(profileInfo.personPhoneData().dialCode());
                Assertions.assertNotNull(profileInfo.personPhoneData().phoneNumber());
                log.info("Profile has valid phone data: dialCode={}, phoneNumber={}",
                        profileInfo.personPhoneData().dialCode(),
                        profileInfo.personPhoneData().phoneNumber());
                break;
            case "device":
                // For device data, we just log since device info comes from client request
                log.info("Profile has valid device information for DEVICE_BIO factor");
                break;
            case "facial":
                // For facial data, we just log since facial info comes from client request
                log.info("Profile has valid facial information for FACIAL factor");
                break;
            default:
                log.warn("Unknown data type: {}", dataType);
        }
    }

    @Given("A profile does not exist in the system")
    public void aProfileDoesNotExistInTheSystem() {
        // Use a profileId that doesn't start with "test-profile-" so mock server returns 404
        profileId = "non-existent-profile-" + UUID.randomUUID();
        log.info("Using non-existent profileId: {} - mock server will return 404", profileId);
    }

    @Given("A profile exists but has no {string} information")
    public void aProfileExistsButHasNoInformation(String dataType) {
        // Use special profileId patterns that mock server can recognize
        switch (dataType) {
            case "phone number":
                profileId = "test-profile-no-phone-" + UUID.randomUUID();
                log.info("Using profileId without phone data: {}", profileId);
                break;
            case "device":
                profileId = "test-profile-no-device-" + UUID.randomUUID();
                log.info("Using profileId without device data: {}", profileId);
                break;
            default:
                log.warn("Unknown data type: {}", dataType);
        }
    }

    // ========================================
    // WHEN STEP DEFINITIONS
    // ========================================

    @When("I initialize a step-up session with {string} factor and {string} with authConfig {string}")
    public void iInitializeAStepUpSessionWithFactorAndIdentifierWithAuthConfig(String factor, String identifierType, String authConfigParams) {
        // Parse factor and identifier type
        AuthFactor authFactor = AuthFactor.valueOf(factor);
        IdentifierType idType = IdentifierType.valueOf(identifierType);

        // Setup authConfig based on factor type and parameters
        Map<AuthFactor, Object> authConfig = new HashMap<>();
        setupAuthConfigForFactorWithParams(authFactor, authConfig, authConfigParams);

        initRequest = InitStepUpSessionDto.builder()
                .authFactorRules(List.of(AuthFactorRule.builder()
                        .factor(authFactor)
                        .build()))
                .authConfig(authConfig)
                .identifierId(profileId)
                .identifierType(idType)
                .flowId(flowId)
                .flowName(flowName)
                .appId(appId)
                .expiredIn(600)
                .build();
        log.info("Initializing step-up session with {} factor and {} for profileId: {} with authConfig: {}",
                factor, identifierType, profileId, authConfigParams);

        try {
            sessionResponse = stepUpService.initStepUpSession(initRequest);
            log.info("Session initialized successfully with authId: {}", sessionResponse.getStepUpAuthId());
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception occurred during {} session initialization: {}", factor, e.getMessage());
        }
    }