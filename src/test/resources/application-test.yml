spring:
  cloud:
    aws:
      region: ${DEFAULT_REGION:ap-southeast-1}
      dynamodb:
        endpoint: http://localhost:4566
      sqs:
        endpoint: http://localhost:4566
        device-bio-enrollment-sqs: tp-step-up-device-bio-enrollment-sqs
    openfeign:
      client:
        config:
          default:
            logger-level: full
          client-profile-ingress:
            url: http://localhost:8100
            api-key: "apiKey"

auth-token:
  request-token:
    link-device-eligibility-check: true