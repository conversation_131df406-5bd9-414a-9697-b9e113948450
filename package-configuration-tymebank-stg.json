{"Parameters": {"ServicesSecurityGroupId": "/tp/authentication/authentication-cluster/services-security-group/group-id", "DefaultCapacityProvider": "FARGATE_SPOT", "ApiPort": "8112", "HealthCheckUrl": "http://localhost:8112/actuator/health/ping", "TaskCPU": "1024", "TaskMemory": "2048", "UseFireLens": "Yes", "UseDataDog": "Yes", "FluentBitVersion": "1.0.4", "FluentBitMaskingRegrexs": "[  {    \"message_pattern\": \"(%d%d%d%d)(%d%d%d%d)(%d%d%d%d)(%d)\",    \"replace_pattern\": \"%1****%3%4\"  },  {    \"message_pattern\": \"(%d%d%d%d)(%d%d%d%d)(%d%d)\",    \"replace_pattern\": \"%1****%3\"  },  {    \"message_pattern\": \"profileId=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"profileId=%1%2***************%6%7\"  },  {    \"message_pattern\": \"profileId: (%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"profileId: %1%2***************%6%7\"  },  {    \"message_pattern\": \"PROFILE_ID#(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"PROFILE_ID#%1%2***************%6%7\"  },  {    \"message_pattern\": \"id: (%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"id: %1%2***************%6%7\"  },  {    \"message_pattern\": \"id=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"id=%1%2***************%6%7\"  },  {    \"message_pattern\": \"idRaw=(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w%w%w%w)(%w%w)\",    \"replace_pattern\": \"idRaw=%1%2***************%6%7\"  }]", "DefaultRegion": "eu-west-1", "LinkDeviceThresholdInDays": "1", "DeviceBioEnrollThresholdInDays": "1", "DeviceBioEnrollmentQueue": "tp-step-up-device-bio-enrollment-sqs", "ClientProfileIngressApiKey": "/tp/integration/apigw/client-profile-ingress/step-up-auth/api-key"}, "Tags": {"tyme:business-unit": "origination-customer", "tyme:app-name": "authentication", "tyme:app-function": "step-up-auth-service", "tyme:environment": "stg", "tyme:requestor": "<EMAIL>", "tyme:entity": "tymebank", "tyme:value-stream": "customer"}}