> Task :compileJava UP-TO-DATE
> Task :processResources UP-TO-DATE
> Task :classes UP-TO-DATE
> Task :compileTestJava UP-TO-DATE
> Task :processTestResources UP-TO-DATE
> Task :testClasses UP-TO-DATE
> Task :test

OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended

> Task :test

StepUpAuthApplicationCucumberBase > Cucumber > Initialization of a Step-Up Session > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Successfully initialize session with DEVICE_BIO factor and PROFILE_ID FAILED
    org.opentest4j.AssertionFailedError at SessionInitAPICucumberSteps.java:127

StepUpAuthApplicationCucumberBase > Cucumber > Initialization of a Step-Up Session > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Initialize session with DEVICE_BIO factor and device linking required FAILED
    org.opentest4j.AssertionFailedError at SessionInitAPICucumberSteps.java:127

StepUpAuthApplicationCucumberBase > Cucumber > Initialization of a Step-Up Session > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Initialize session with DEVICE_BIO factor and existing linked device FAILED
    org.opentest4j.AssertionFailedError at SessionInitAPICucumberSteps.java:127

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate active session with matching factor > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.2 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:153

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate active session with matching factor > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.3 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:153

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate session with includeFactorConfig flag > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.1 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:212

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate session with includeFactorConfig flag > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.2 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:153

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate session with includeFactorConfig flag > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.3 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:153

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validation fails for factor mismatch > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.1 FAILED
    java.lang.IllegalArgumentException at Enum.java:293

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validation fails for factor mismatch > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.2 FAILED
    java.lang.IllegalArgumentException at Enum.java:293

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validation fails for factor mismatch > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.3 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:237

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate session state consistency > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.2 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:153

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate session with complex factor configuration > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.1 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:153

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate session with complex factor configuration > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.2 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:153

StepUpAuthApplicationCucumberBase > Cucumber > Session Validation API Component Testing > Validate session with factor history > Examples > com.tyme.tymex.stepupauth.StepUpAuthApplicationCucumberBase.Example #1.2 FAILED
    org.opentest4j.AssertionFailedError at SessionValidationAPICucumberSteps.java:153

50 tests completed, 15 failed

> Task :test FAILED
> Task :jacocoTestReport

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':test'.
> There were failing tests. See the report at: file:///Users/<USER>/Desktop/componentLearn/tc-mx-step-up-auth-svc/build/reports/tests/test/index.html

* Try:
> Run with --scan to get full insights.

BUILD FAILED in 57s
6 actionable tasks: 2 executed, 4 up-to-date
